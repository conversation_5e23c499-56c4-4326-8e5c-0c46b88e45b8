# Google reCAPTCHA v3 Setup Instructions

This document provides instructions for setting up Google reCAPTCHA v3 integration in the contact form.

## Prerequisites

1. A Google account
2. Access to the Google reCAPTCHA Admin Console

## Setup Steps

### 1. Create reCAPTCHA Site Keys

1. Go to [Google reCAPTCHA Admin Console](https://www.google.com/recaptcha/admin/create)
2. Click "Create" to add a new site
3. Fill in the form:
   - **Label**: Enter a name for your site (e.g., "Beilun Contact Form")
   - **reCAPTCHA type**: Select "reCAPTCHA v3"
   - **Domains**: Add your domain(s):
     - For development: `localhost`
     - For production: your actual domain (e.g., `yourdomain.com`)
   - **Owners**: Add additional Google accounts if needed
4. Accept the reCAPTCHA Terms of Service
5. Click "Submit"

### 2. Configure Environment Variables

1. Copy your **Site Key** and **Secret Key** from the reCAPTCHA admin console
2. Open the `.env.local` file in your project root
3. Replace the placeholder values:

```env
# Google reCAPTCHA v3 Configuration
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=your_actual_site_key_here
RECAPTCHA_SECRET_KEY=your_actual_secret_key_here
```

**Important**: 
- The `NEXT_PUBLIC_RECAPTCHA_SITE_KEY` is used on the client-side and must start with `NEXT_PUBLIC_`
- The `RECAPTCHA_SECRET_KEY` is used on the server-side and should NOT have the `NEXT_PUBLIC_` prefix

### 3. Test the Integration

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Navigate to the contact page: `http://localhost:3000/contact`

3. You should see:
   - A security verification status indicator above the form
   - The submit button should be disabled until reCAPTCHA verification completes
   - After verification, the button should become enabled

### 4. Verify reCAPTCHA is Working

1. Fill out the contact form with valid information
2. Click the "送信する" (Submit) button
3. Check the browser's developer console for any errors
4. Check your server logs to see the form submission

## Features Implemented

### Client-Side Features
- **Automatic verification**: reCAPTCHA verification starts automatically when the page loads
- **Visual feedback**: Status indicator shows verification progress and results
- **Button state management**: Submit button is disabled until verification completes
- **Error handling**: Displays appropriate messages for verification failures
- **Retry functionality**: Users can retry verification if it fails

### Server-Side Features
- **Token verification**: Server validates reCAPTCHA tokens with Google's API
- **Score checking**: For reCAPTCHA v3, checks that the score is above 0.5 (human-like behavior)
- **Error handling**: Proper error responses for invalid tokens or verification failures

## Customization Options

### Adjusting reCAPTCHA Score Threshold

In `src/app/api/contact/route.ts`, you can adjust the minimum score required:

```typescript
// Current threshold is 0.5 (moderate security)
return data.success && data.score >= 0.5

// For stricter security, use 0.7 or higher
return data.success && data.score >= 0.7

// For more lenient security, use 0.3 or lower
return data.success && data.score >= 0.3
```

### Customizing Verification Actions

In `src/hooks/use-recaptcha.ts` and `src/components/contact/contact-form.tsx`, you can customize the action names:

```typescript
// Different actions for different form interactions
executeRecaptcha('contact_form_load')    // When form loads
executeRecaptcha('contact_form_submit')  // When form is submitted
executeRecaptcha('contact_form_retry')   // When user retries verification
```

## Troubleshooting

### Common Issues

1. **"reCAPTCHA site key is not configured" warning**
   - Check that `NEXT_PUBLIC_RECAPTCHA_SITE_KEY` is set in `.env.local`
   - Restart your development server after adding environment variables

2. **"reCAPTCHA verification failed" error**
   - Verify that your secret key is correct in `.env.local`
   - Check that your domain is added to the reCAPTCHA site configuration
   - Ensure you're using reCAPTCHA v3 (not v2)

3. **Submit button remains disabled**
   - Check browser console for JavaScript errors
   - Verify that the reCAPTCHA script is loading properly
   - Try refreshing the page

4. **Form submission fails**
   - Check server logs for detailed error messages
   - Verify API route is accessible at `/api/contact`
   - Test reCAPTCHA verification manually

### Development vs Production

- **Development**: Use `localhost` as the domain in reCAPTCHA configuration
- **Production**: Add your actual domain to the reCAPTCHA configuration
- **Testing**: reCAPTCHA works on both HTTP (localhost) and HTTPS (production)

## Security Considerations

1. **Never expose the secret key**: The `RECAPTCHA_SECRET_KEY` should only be used server-side
2. **Validate on server**: Always verify reCAPTCHA tokens on the server, never trust client-side validation alone
3. **Monitor scores**: Keep track of reCAPTCHA scores to adjust thresholds as needed
4. **Rate limiting**: Consider implementing additional rate limiting for form submissions

## Additional Resources

- [Google reCAPTCHA v3 Documentation](https://developers.google.com/recaptcha/docs/v3)
- [reCAPTCHA Admin Console](https://www.google.com/recaptcha/admin)
- [Next.js Environment Variables](https://nextjs.org/docs/basic-features/environment-variables)

"use client"

import Link from "next/link"
import Image from "next/image"
import { useState, useEffect } from "react"
import { Mail } from "lucide-react"

interface NewsItem {
  id: number
  documentId: string
  title: string
  newsbody: string
  createdAt: string
  updatedAt: string
  publishedAt: string
  Classification: string
}

interface NewsResponse {
  data: NewsItem[]
  meta: {
    pagination: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

export function NewsSection() {
  const [news, setNews] = useState<NewsItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchNews = async () => {
      try {
        setLoading(true)
        const response = await fetch('http://43.153.145.176:1337/api/news2?pagination[page]=1&pagination[pageSize]=3&sort=publishedAt:desc')

        if (!response.ok) {
          throw new Error('Failed to fetch news')
        }

        const data: NewsResponse = await response.json()
        setNews(data.data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchNews()
  }, [])

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '.')
  }

  const getClassificationLabel = (classification: string) => {
    // Remove "info" prefix if present and return the label
    return classification.replace(/^info/, '')
  }
  return (
    <div className="pt-12 pb-6 bg-white">
      <div className="container-huari">
        <div className="text-center mb-8">
          <h2 className="text-2xl sm:text-3xl font-bold mb-8">お知らせ</h2>

          <div className="text-right mb-6">
            <Link
              href="/news"
              className="inline-block text-huari-red text-sm font-medium border border-huari-red px-4 py-2 hover:bg-huari-red hover:text-white transition-colors duration-200"
            >
              一覧はこちら
            </Link>
          </div>

          <div className="space-y-4 max-w-4xl mx-auto">
            {loading ? (
              <div className="text-center py-8">
                <p className="text-gray-500">読み込み中...</p>
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-red-500">エラーが発生しました: {error}</p>
              </div>
            ) : news.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">お知らせはありません</p>
              </div>
            ) : (
              news.map((item) => (
                <div key={item.id} className="border-b border-gray-200 pb-4 text-left">
                  <Link href={`/news/${item.documentId}`} className="block hover:text-huari-red transition-colors">
                    <p className="text-sm text-gray-500 mb-1">
                      {formatDate(item.publishedAt)}
                      {item.Classification && (
                        <span className="ml-2 text-xs bg-gray-200 text-gray-700 px-2 py-0.5 rounded">
                          {getClassificationLabel(item.Classification)}
                        </span>
                      )}
                    </p>
                    <p className="font-medium">{item.title}</p>
                  </Link>
                </div>
              ))
            )}
          </div>
        </div>

      </div>

      {/* Contact section - full width gray background like original */}
      <div className="bg-gray-100 py-12">
        <div className="container-huari text-center">
          <h3 className="text-xl font-bold mb-2 text-huari-red">CONTACT US</h3>
          <p className="text-lg font-bold mb-8">お見積り・ご相談はこちら</p>

          {/* Phone and Contact button in horizontal layout */}
          <div className="flex items-center justify-center gap-6 mb-4">
            <div className="text-center">
              <a href="tel:0723612111" className="text-4xl font-bold text-huari-red hover:text-huari-red/80 transition-colors block">
                +(86) 0574-86118978
              </a>
              <p className="text-sm text-gray-600 mt-1">
                営業時間 9：00～17：00（土日祝 定休）
              </p>
            </div>

            <Link
              href="/contact"
              className="inline-flex items-center gap-2 bg-huari-red text-white px-8 py-3 hover:bg-huari-red/90 transition-colors font-medium"
            >
              <Mail size={24} className="mr-2" />
              お問い合わせ
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

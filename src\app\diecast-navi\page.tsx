"use client"

import Image from "next/image"
import Link from "next/link"
import { useState, useEffect } from "react"

export default function DiecastNaviPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Slider Section */}
      <HeroSliderSection />
      
      {/* Resolution Policy Section */}
      <ResolutionPolicySection />
      
      {/* Services Section */}
      <ServicesSection />
      
      {/* Features/Advantages Section */}
      <FeaturesSection />
      
      {/* Examples Section */}
      <ExamplesSection />
      
      {/* News Section */}
      <NewsSection />
      
      {/* Contact Section */}
      <ContactSection />
    </div>
  )
}

// Hero Slider Component
function HeroSliderSection() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)

  // 轮播图数据 - 根据原始网站的轮播内容
  const slides = [
    {
      id: 1,
      image: "images/diecast/yazhu-1.jpg",
      alt: "ロストワックス鋳造品・砂型鋳造品・切削加工品をご使用中の皆様 ダイカスト鋳造の特許製法により劇的なコストダウン・高品質化を実現します",
      link: "/diecast-navi/contact"
    },
    {
      id: 2,
      image: "images/diecast/muju-1.jpg",
      alt: "ダイカスト鋳造 コストダウンNavi - 特許製法による劇的なコストダウン",
      link: "/diecast-navi/contact"
    },
    {
      id: 3,
      image: "images/diecast/jijia-2.jpg",
      alt: "高品質・低コスト・短納期を実現するダイカスト特許製法",
      link: "/diecast-navi/contact"
    },
    {
      id: 4,
      image: "images/diecast/diandu-1.jpg",
      alt: "華日パーツのダイカスト技術で製造コストを大幅削減",
      link: "/diecast-navi/contact"
    }
  ]

  // 自动轮播 - 支持暂停
  useEffect(() => {
    if (!isPlaying) return

    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, 5000) // 5秒切换一次

    return () => clearInterval(timer)
  }, [slides.length, isPlaying])

  // 键盘导航
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'ArrowLeft') {
        prevSlide()
      } else if (event.key === 'ArrowRight') {
        nextSlide()
      } else if (event.key === ' ') {
        event.preventDefault()
        setIsPlaying(!isPlaying)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isPlaying])

  // 手动切换到指定幻灯片
  const goToSlide = (index: number) => {
    setCurrentSlide(index)
  }

  // 上一张
  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
  }

  // 下一张
  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length)
  }

  return (
    <div
      className="relative w-full h-[400px] md:h-[600px] overflow-hidden bg-gradient-to-br from-blue-800 via-blue-700 to-blue-900"
      onMouseEnter={() => setIsPlaying(false)}
      onMouseLeave={() => setIsPlaying(true)}
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-900/20 to-transparent"></div>

      {/* 轮播图片容器 */}
      <div className="relative w-full h-full">
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute inset-0 transition-all duration-1000 ease-in-out ${
              index === currentSlide
                ? 'opacity-100 transform translate-x-0'
                : index < currentSlide
                  ? 'opacity-0 transform -translate-x-full'
                  : 'opacity-0 transform translate-x-full'
            }`}
          >
            <Link href={slide.link} className="block w-full h-full">
              <Image
                src={slide.image}
                alt={slide.alt}
                fill
                priority={index === 0}
                className="object-contain"
              />
            </Link>
          </div>
        ))}
      </div>

      {/* 左右箭头 - 更精致的设计 */}
      <button
        onClick={prevSlide}
        className="absolute left-2 md:left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white p-2 md:p-3 rounded-full transition-all duration-300 z-20 border border-white/30"
        aria-label="前の画像"
      >
        <svg className="w-4 h-4 md:w-6 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
      </button>

      <button
        onClick={nextSlide}
        className="absolute right-2 md:right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white p-2 md:p-3 rounded-full transition-all duration-300 z-20 border border-white/30"
        aria-label="次の画像"
      >
        <svg className="w-4 h-4 md:w-6 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </button>

      {/* 底部指示器 - 更现代的设计 */}
      <div className="absolute bottom-16 md:bottom-20 left-1/2 transform -translate-x-1/2 flex space-x-3 z-20">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`transition-all duration-300 ${
              index === currentSlide
                ? 'w-8 h-3 bg-white rounded-full'
                : 'w-3 h-3 bg-white/50 hover:bg-white/70 rounded-full'
            }`}
            aria-label={`スライド ${index + 1} に移動`}
          />
        ))}
      </div>

      {/* 播放/暂停按钮 */}
      <button
        onClick={() => setIsPlaying(!isPlaying)}
        className="absolute top-4 right-4 bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white p-2 rounded-full transition-all duration-300 z-20 border border-white/30"
        aria-label={isPlaying ? "轮播を停止" : "轮播を開始"}
      >
        {isPlaying ? (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
          </svg>
        ) : (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8 5v14l11-7z"/>
          </svg>
        )}
      </button>

      {/* 轮播进度条 */}
      <div className="absolute bottom-0 left-0 w-full h-1 bg-white/20 z-10">
        <div
          className="h-full bg-orange-500 transition-all duration-300 ease-linear"
          style={{ width: `${((currentSlide + 1) / slides.length) * 100}%` }}
        />
      </div>

      {/* 底部按钮 */}
      <div className="absolute bottom-4 md:bottom-8 left-1/2 transform -translate-x-1/2 px-4 z-10">
        <Link
          href="/diecast-navi/contact"
          className="inline-block bg-orange-500 hover:bg-orange-600 text-white px-6 md:px-12 py-3 md:py-4 rounded-sm font-bold text-lg md:text-xl transition-colors shadow-lg"
        >
          お見積り・お問い合わせ
        </Link>
      </div>
    </div>
  )
}

// Resolution Policy Section
function ResolutionPolicySection() {
  return (
    <div className="relative">
      {/* Red background header section */}
      <div className="bg-red-600 py-8 relative overflow-hidden">
        <div className="container-huari">
          <div className="text-center relative">
            {/* Navigation arrows - positioned relative to text */}
            <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-8 text-white text-3xl cursor-pointer hover:text-gray-200 transition-colors">
              ▶
            </div>
            <div className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-8 text-white text-3xl cursor-pointer hover:text-gray-200 transition-colors">
              ◀
            </div>

            <h2 className="text-2xl md:text-4xl font-bold text-white leading-tight px-16">
              <span className="text-yellow-300">"劇的"</span>コストダウンを実現できる<br />
              ダイカスト特許製法の特徴
            </h2>
          </div>
        </div>
      </div>

      {/* Main content section with background image */}
      <div
        className="relative min-h-[500px] bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('images/diecast/shengchan-banner.jpg')"
        }}
      >
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-black bg-opacity-20"></div>

        <div className="relative z-10 container-huari py-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
            {/* Left side - Empty space for background image */}
            <div className="hidden lg:block"></div>

            {/* Right side - Resolution policy cards */}
            <div className="bg-white bg-opacity-95 p-6 rounded-lg shadow-lg">
              <div className="mb-6">
                <p className="text-xs text-gray-500 mb-1">Resolution policy</p>
                <h3 className="text-lg font-bold text-gray-800">解決ポリシー</h3>
              </div>

              <div className="space-y-3">
                {/* Card 1 - 金型費削減 */}
                <div className="border-2 border-red-400 bg-white rounded-sm shadow-sm">
                  <div className="p-4">
                    <div className="bg-orange-500 text-white text-xs font-bold py-1 px-3 rounded-sm inline-block mb-3">
                      金型費削減
                    </div>
                    <p className="text-sm text-gray-700 leading-relaxed">
                      従来のダイカストと比較し、<br />
                      金型費用を<span className="text-red-500 font-bold text-lg">50%</span>削減します！
                    </p>
                  </div>
                </div>

                {/* Card 2 - ロット削減 */}
                <div className="border-2 border-red-400 bg-white rounded-sm shadow-sm">
                  <div className="p-4">
                    <div className="bg-yellow-500 text-white text-xs font-bold py-1 px-3 rounded-sm inline-block mb-3">
                      ロット削減
                    </div>
                    <p className="text-sm text-gray-700 leading-relaxed">
                      段取り替えが大幅に短縮できるため、<br />
                      <span className="text-red-500 font-bold text-lg">50個</span>/ロットからの生産が可能です！
                    </p>
                  </div>
                </div>

                {/* Card 3 - コストダウン */}
                <div className="border-2 border-red-400 bg-white rounded-sm shadow-sm">
                  <div className="p-4">
                    <div className="bg-green-500 text-white text-xs font-bold py-1 px-3 rounded-sm inline-block mb-3">
                      コストダウン
                    </div>
                    <p className="text-sm text-gray-700 leading-relaxed">
                      ロストワックス・砂型鋳造・切削と比較し、<br />
                      製造コストを<span className="text-red-500 font-bold text-lg">50%</span>以上削減します！
                    </p>
                  </div>
                </div>

                {/* Card 4 - リードタイム短縮 */}
                <div className="border-2 border-red-400 bg-white rounded-sm shadow-sm">
                  <div className="p-4">
                    <div className="bg-red-500 text-white text-xs font-bold py-1 px-3 rounded-sm inline-block mb-3">
                      リードタイム短縮
                    </div>
                    <p className="text-sm text-gray-700 leading-relaxed">
                      ロストワックス・砂型鋳造と比較し、<br />
                      製造リードタイムを<span className="text-red-500 font-bold text-lg">50%</span>以上短縮します！
                    </p>
                  </div>
                </div>
              </div>

              {/* Button */}
              <div className="mt-6 text-center">
                <Link
                  href="/diecast-navi/feature"
                  className="inline-block bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-full font-bold text-sm transition-colors shadow-md"
                >
                  詳しくはこちら
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Services Section
function ServicesSection() {
  return (
    <div className="py-16 bg-gray-100">
      <div className="container-huari">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-800 mb-4 leading-tight">
            ダイカスト特許製法を用いて<br />
            皆様に提供するサービス
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <Link href="/diecast-navi/service" className="block bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow overflow-hidden border border-gray-200">
            <Image
              src="https://www.huariparts.co.jp/diecast-navi/wp-content/themes/diecast-navi/images/top/top_con4.png"
              alt="ロストワックス鋳造品をご使用中の皆様へ"
              width={400}
              height={250}
              className="w-full h-48 object-cover"
            />
            <div className="p-6">
              <h3 className="font-bold text-center text-lg">
                <strong className="text-blue-600">ロストワックス鋳造品</strong>をご使用中の皆様へ
              </h3>
            </div>
          </Link>

          <Link href="/diecast-navi/service/sand-casting" className="block bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow overflow-hidden border border-gray-200">
            <Image
              src="https://www.huariparts.co.jp/diecast-navi/wp-content/themes/diecast-navi/images/top/top_con5.png"
              alt="砂型鋳造品をご使用中の皆様へ"
              width={400}
              height={250}
              className="w-full h-48 object-cover"
            />
            <div className="p-6">
              <h3 className="font-bold text-center text-lg">
                <strong className="text-blue-600">砂型鋳造品</strong> をご使用中の皆様へ
              </h3>
            </div>
          </Link>

          <Link href="/diecast-navi/service/cut-product" className="block bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow overflow-hidden border border-gray-200">
            <Image
              src="https://www.huariparts.co.jp/diecast-navi/wp-content/themes/diecast-navi/images/top/top_con6.png"
              alt="切削品をご使用中の皆様へ"
              width={400}
              height={250}
              className="w-full h-48 object-cover"
            />
            <div className="p-6">
              <h3 className="font-bold text-center text-lg">
                <strong className="text-blue-600">切削品</strong> をご使用中の皆様へ
              </h3>
            </div>
          </Link>

          <Link href="/diecast-navi/service/screw-fastening" className="block bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow overflow-hidden border border-gray-200">
            <Image
              src="https://www.huariparts.co.jp/diecast-navi/wp-content/themes/diecast-navi/images/top/top_con7.png"
              alt="複合品（ビス締結）をご使用中の皆様へ"
              width={400}
              height={250}
              className="w-full h-48 object-cover"
            />
            <div className="p-6">
              <h3 className="font-bold text-center text-lg">
                <strong className="text-blue-600">複合品(ビス締結)</strong> をご使用中の皆様へ
              </h3>
            </div>
          </Link>

          <Link href="/diecast-navi/service/welding" className="block bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow overflow-hidden border border-gray-200">
            <Image
              src="https://www.huariparts.co.jp/diecast-navi/wp-content/themes/diecast-navi/images/top/top_con8.png"
              alt="複合品（溶接）をご使用中の皆様へ"
              width={400}
              height={250}
              className="w-full h-48 object-cover"
            />
            <div className="p-6">
              <h3 className="font-bold text-center text-lg">
                <strong className="text-blue-600">複合品(溶接)</strong> をご使用中の皆様へ
              </h3>
            </div>
          </Link>

          <Link href="/diecast-navi/service/die-casting" className="block bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow overflow-hidden border border-gray-200">
            <Image
              src="https://www.huariparts.co.jp/diecast-navi/wp-content/themes/diecast-navi/images/top/top_con9.png"
              alt="従来のダイカストをご使用中の皆様へ"
              width={400}
              height={250}
              className="w-full h-48 object-cover"
            />
            <div className="p-6">
              <h3 className="font-bold text-center text-lg">
                <strong className="text-blue-600">従来のダイカスト</strong> をご使用中の皆様へ
              </h3>
            </div>
          </Link>
        </div>
      </div>
    </div>
  )
}

// Features/Advantages Section
function FeaturesSection() {
  return (
    <div className="py-16 bg-gray-50">
      <div className="container-huari">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">
            ダイカスト鋳造 コストダウンNaviの<br />
            特徴と優位性
          </h2>
        </div>

        {/* First row - 4 cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          {/* Card 01 */}
          <Link href="/diecast-navi/advantages#c1" className="block bg-white border-2 border-gray-300 shadow-lg hover:shadow-xl transition-shadow overflow-hidden">
            <Image
              src="https://www.huariparts.co.jp/diecast-navi/wp-content/themes/diecast-navi/images/top/top_con12.png"
              alt="ダイカスト鋳造の特許製法で初期コストを劇的削減！"
              width={400}
              height={250}
              className="w-full h-48 object-cover"
            />
            <div className="bg-black text-white p-4 relative">
              <p className="text-sm leading-relaxed mb-2">
                ダイカスト鋳造の特許製法で<br />
                <span className="text-yellow-400 font-bold">初期コストを"劇的削減"！</span>
              </p>
              <div className="absolute bottom-2 right-2">
                <span className="text-red-500 text-4xl font-bold">01</span>
              </div>
            </div>
          </Link>

          {/* Card 02 */}
          <Link href="/diecast-navi/advantages#c2" className="block bg-white border-2 border-gray-300 shadow-lg hover:shadow-xl transition-shadow overflow-hidden">
            <Image
              src="https://www.huariparts.co.jp/diecast-navi/wp-content/themes/diecast-navi/images/top/top_con13.png"
              alt="他工法からダイカストへの工法変換でコストダウン！"
              width={400}
              height={250}
              className="w-full h-48 object-cover"
            />
            <div className="bg-black text-white p-4 relative">
              <p className="text-sm leading-relaxed mb-2">
                他工法からダイカストへの工法変換で<br />
                <span className="text-yellow-400 font-bold">コストダウン！</span>
              </p>
              <div className="absolute bottom-2 right-2">
                <span className="text-red-500 text-4xl font-bold">02</span>
              </div>
            </div>
          </Link>

          {/* Card 03 */}
          <Link href="/diecast-navi/advantages#c3" className="block bg-white border-2 border-gray-300 shadow-lg hover:shadow-xl transition-shadow overflow-hidden">
            <Image
              src="https://www.huariparts.co.jp/diecast-navi/wp-content/themes/diecast-navi/images/top/top_con14.png"
              alt="金型設計・製作～機械加工・表面処理まで一貫対応！"
              width={400}
              height={250}
              className="w-full h-48 object-cover"
            />
            <div className="bg-black text-white p-4 relative">
              <p className="text-sm leading-relaxed mb-2">
                金型設計・製作～機械加工・<br />
                表面処理まで<span className="text-yellow-400 font-bold">一貫対応！</span>
              </p>
              <div className="absolute bottom-2 right-2">
                <span className="text-red-500 text-4xl font-bold">03</span>
              </div>
            </div>
          </Link>

          {/* Card 04 */}
          <Link href="/diecast-navi/advantages#c4" className="block bg-white border-2 border-gray-300 shadow-lg hover:shadow-xl transition-shadow overflow-hidden">
            <Image
              src="https://www.huariparts.co.jp/diecast-navi/wp-content/themes/diecast-navi/images/top/top_con15.png"
              alt="高品質化・コストダウンを実現する設計提案能力！"
              width={400}
              height={250}
              className="w-full h-48 object-cover"
            />
            <div className="bg-black text-white p-4 relative">
              <p className="text-sm leading-relaxed mb-2">
                高品質化・コストダウンを実現する<br />
                <span className="text-yellow-400 font-bold">設計提案能力！</span>
              </p>
              <div className="absolute bottom-2 right-2">
                <span className="text-red-500 text-4xl font-bold">04</span>
              </div>
            </div>
          </Link>
        </div>

        {/* Second row - 3 cards centered */}
        <div className="flex justify-center mb-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl">
            {/* Card 05 */}
            <Link href="/diecast-navi/advantages#c5" className="block bg-white border-2 border-gray-300 shadow-lg hover:shadow-xl transition-shadow overflow-hidden">
              <Image
                src="https://www.huariparts.co.jp/diecast-navi/wp-content/themes/diecast-navi/images/top/top_con16.png"
                alt="CTをはじめとした充実した検査設備で品質管理を徹底！"
                width={400}
                height={250}
                className="w-full h-48 object-cover"
              />
              <div className="bg-black text-white p-4 relative">
                <p className="text-sm leading-relaxed mb-2">
                  CTをはじめとした充実した検査設備で<br />
                  <span className="text-yellow-400 font-bold">品質管理を徹底！</span>
                </p>
                <div className="absolute bottom-2 right-2">
                  <span className="text-red-500 text-4xl font-bold">05</span>
                </div>
              </div>
            </Link>

            {/* Card 06 */}
            <Link href="/diecast-navi/advantages#c6" className="block bg-white border-2 border-gray-300 shadow-lg hover:shadow-xl transition-shadow overflow-hidden">
              <Image
                src="https://www.huariparts.co.jp/diecast-navi/wp-content/themes/diecast-navi/images/top/top_con17.png"
                alt="アルミ、亜鉛、マグネシウムの多品種生産に対応！"
                width={400}
                height={250}
                className="w-full h-48 object-cover"
              />
              <div className="bg-black text-white p-4 relative">
                <p className="text-sm leading-relaxed mb-2">
                  アルミ、亜鉛、マグネシウムの<br />
                  <span className="text-yellow-400 font-bold">多品種生産に対応！</span>
                </p>
                <div className="absolute bottom-2 right-2">
                  <span className="text-red-500 text-4xl font-bold">06</span>
                </div>
              </div>
            </Link>

            {/* Card 07 */}
            <Link href="/diecast-navi/advantages#c7" className="block bg-white border-2 border-gray-300 shadow-lg hover:shadow-xl transition-shadow overflow-hidden">
              <Image
                src="https://www.huariparts.co.jp/diecast-navi/wp-content/themes/diecast-navi/images/top/top_con18.png"
                alt="1650tのダイカスト鋳造が可能 大型ダイカスト品の提供！"
                width={400}
                height={250}
                className="w-full h-48 object-cover"
              />
              <div className="bg-black text-white p-4 relative">
                <p className="text-sm leading-relaxed mb-2">
                  1650tのダイカスト鋳造が可能<br />
                  <span className="text-yellow-400 font-bold">大型ダイカスト品の提供！</span>
                </p>
                <div className="absolute bottom-2 right-2">
                  <span className="text-red-500 text-4xl font-bold">07</span>
                </div>
              </div>
            </Link>
          </div>
        </div>

        <div className="text-center">
          <Link
            href="/diecast-navi/advantages"
            className="inline-block bg-red-500 hover:bg-red-600 text-white px-12 py-3 rounded-full font-medium transition-colors shadow-md"
          >
            詳しくはこちら
          </Link>
        </div>
      </div>
    </div>
  )
}

// Examples Section with Filtering
function ExamplesSection() {
  return (
    <div>
      {/* Red background section - Title and Filters */}
      <div className="py-16 bg-red-600">
        <div className="container-huari">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-white mb-6">
              ダイカスト鋳造 コストダウンNaviの<br />
              工法変換事例・製品事例
            </h2>
          </div>

          {/* Filter Tabs - Yellow oval buttons */}
          <div className="mb-6">
            <div className="flex flex-wrap justify-center gap-3 mb-6">
              <button className="px-6 py-2 bg-yellow-400 text-black rounded-full font-medium hover:bg-yellow-300 transition-colors">業界から探す</button>
              <button className="px-6 py-2 bg-white text-black rounded-full font-medium hover:bg-gray-100 transition-colors">材質から探す</button>
              <button className="px-6 py-2 bg-white text-black rounded-full font-medium hover:bg-gray-100 transition-colors">従来工法から探す</button>
              <button className="px-6 py-2 bg-white text-black rounded-full font-medium hover:bg-gray-100 transition-colors">対応範囲から探す</button>
            </div>

            {/* Filter Grid - Black background with white text */}
            <div className="bg-black bg-opacity-80 p-6 rounded-lg max-w-4xl mx-auto">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                {/* Row 1 */}
                <button className="px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">すべて</button>
                <button className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">照明機器</button>
                <button className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">医療機器</button>
                <button className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">自動車</button>

                {/* Row 2 */}
                <button className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">食品機械</button>
                <button className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">検査機器</button>
                <button className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">遊具</button>
                <button className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">ロボット</button>

                {/* Row 3 */}
                <button className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">半導体製造装置</button>
                <button className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">建築</button>
                <button className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">搬送機器</button>
                <button className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">理容・美容機器</button>

                {/* Row 4 */}
                <button className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">アミューズメント機器</button>
                <button className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">産業機械</button>
                <button className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">インフラ</button>
                <button className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">電子機器</button>

                {/* Row 5 */}
                <button className="px-3 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors">その他</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* White background section - Product Cards */}
      <div className="py-8 bg-white">
        <div className="container-huari">
          {/* Example Cards Grid - 4 columns layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Link href="/diecast-navi/example/932.html" className="block bg-white border border-gray-200 shadow-md hover:shadow-lg transition-shadow overflow-hidden">
              <div className="relative">
                <Image
                  src="https://www.huariparts.co.jp/diecast-navi/wp-content/uploads/2025/05/d15784b5c2bd529108e5571f5f55bceb-300x205.jpg"
                  alt="医療機器用スイッチパネル"
                  width={300}
                  height={205}
                  className="w-full h-40 object-cover"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-80 text-white p-2">
                  <h3 className="font-bold text-sm">医療機器用スイッチパネル</h3>
                </div>
              </div>
              <div className="p-3">
                <div className="mb-2">
                  <span className="bg-red-500 text-white px-2 py-1 text-xs font-bold">コスト</span>
                  <span className="ml-2 text-red-500 font-bold text-2xl">70</span>
                  <span className="text-red-500 font-bold text-sm">％削減</span>
                </div>
                <div className="text-xs text-gray-600 space-y-1">
                  <div><span className="font-medium">業　界:</span> 医療機器</div>
                  <div><span className="font-medium">材　質:</span> ADC12</div>
                  <div><span className="font-medium">従来工法:</span> ロストワックス</div>
                </div>
              </div>
            </Link>

            <Link href="/diecast-navi/example/930.html" className="block bg-white border border-gray-200 shadow-md hover:shadow-lg transition-shadow overflow-hidden">
              <div className="relative">
                <Image
                  src="https://www.huariparts.co.jp/diecast-navi/wp-content/uploads/2025/05/c513bf3a4c3f0f9ea3d9a7f62c1f0595-300x205.jpg"
                  alt="医療機器用操作パネル"
                  width={300}
                  height={205}
                  className="w-full h-40 object-cover"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-80 text-white p-2">
                  <h3 className="font-bold text-sm">医療機器用操作パネル</h3>
                </div>
              </div>
              <div className="p-3">
                <div className="mb-2">
                  <span className="bg-red-500 text-white px-2 py-1 text-xs font-bold">コスト</span>
                  <span className="ml-2 text-red-500 font-bold text-2xl">50</span>
                  <span className="text-red-500 font-bold text-sm">％削減</span>
                </div>
                <div className="text-xs text-gray-600 space-y-1">
                  <div><span className="font-medium">業　界:</span> 医療機器</div>
                  <div><span className="font-medium">材　質:</span> ADC12</div>
                  <div><span className="font-medium">従来工法:</span> ロストワックス</div>
                </div>
              </div>
            </Link>

            <Link href="/diecast-navi/example/928.html" className="block bg-white border border-gray-200 shadow-md hover:shadow-lg transition-shadow overflow-hidden">
              <div className="relative">
                <Image
                  src="https://www.huariparts.co.jp/diecast-navi/wp-content/uploads/2025/05/7810b4b337eb696fff2f5ef027d92440-300x205.jpg"
                  alt="電線加工機用ブラケット"
                  width={300}
                  height={205}
                  className="w-full h-40 object-cover"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-80 text-white p-2">
                  <h3 className="font-bold text-sm">電線加工機用ブラケット</h3>
                </div>
              </div>
              <div className="p-3">
                <div className="mb-2">
                  <span className="bg-red-500 text-white px-2 py-1 text-xs font-bold">コスト</span>
                  <span className="ml-2 text-red-500 font-bold text-2xl">40</span>
                  <span className="text-red-500 font-bold text-sm">％削減</span>
                  <span className="bg-blue-500 text-white px-2 py-1 text-xs font-bold ml-2">リードタイム</span>
                  <span className="ml-1 text-blue-500 font-bold text-xl">20</span>
                  <span className="text-blue-500 font-bold text-sm">％短縮</span>
                </div>
                <div className="text-xs text-gray-600 space-y-1">
                  <div><span className="font-medium">業　界:</span> 産業機械</div>
                  <div><span className="font-medium">材　質:</span> ADC12</div>
                  <div><span className="font-medium">従来工法:</span> 切削加工</div>
                </div>
              </div>
            </Link>

            <Link href="/diecast-navi/example/920.html" className="block bg-white border border-gray-200 shadow-md hover:shadow-lg transition-shadow overflow-hidden">
              <div className="relative">
                <Image
                  src="https://www.huariparts.co.jp/diecast-navi/wp-content/uploads/2025/04/c7a7a583cbf558c995a473ab63ea630a-300x205.jpg"
                  alt="電線加工機用ベース部品"
                  width={300}
                  height={205}
                  className="w-full h-40 object-cover"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-80 text-white p-2">
                  <h3 className="font-bold text-sm">電線加工機用ベース部品</h3>
                </div>
              </div>
              <div className="p-3">
                <div className="mb-2">
                  <span className="bg-red-500 text-white px-2 py-1 text-xs font-bold">コスト</span>
                  <span className="ml-2 text-red-500 font-bold text-2xl">40</span>
                  <span className="text-red-500 font-bold text-sm">％削減</span>
                  <span className="bg-blue-500 text-white px-2 py-1 text-xs font-bold ml-2">リードタイム</span>
                  <span className="ml-1 text-blue-500 font-bold text-xl">20</span>
                  <span className="text-blue-500 font-bold text-sm">％短縮</span>
                </div>
                <div className="text-xs text-gray-600 space-y-1">
                  <div><span className="font-medium">業　界:</span> 産業機械</div>
                  <div><span className="font-medium">材　質:</span> ADC12</div>
                  <div><span className="font-medium">従来工法:</span> 切削加工</div>
                </div>
              </div>
            </Link>
          </div>

          <div className="text-center">
            <Link
              href="/diecast-navi/example"
              className="inline-flex items-center bg-red-500 hover:bg-red-600 text-white px-8 py-3 rounded-full font-medium transition-colors"
            >
              一覧へ
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

// News Section
function NewsSection() {
  return (
    <div className="py-16 bg-gray-100">
      <div className="container-huari">
        <div className="mb-12 text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-8">
            <span className="text-red-500">NEWS</span><br />
            新着情報
          </h2>
        </div>

        <div className="max-w-3xl mx-auto mb-12">
          <div className="space-y-0">
            <div className="py-4 border-b border-gray-300">
              <div className="space-y-2">
                <div className="text-1xl text-gray-700 font-medium">2021.09.14</div>
                <Link href="/diecast-navi/news/604.html" className="block text-gray-800 hover:text-red-500 transition-colors leading-relaxed text-1xl">
                  鋳造コストにお悩みの方必見！VA・VE技術ハンドブックの無料DLが可能！
                </Link>
              </div>
            </div>

            <div className="py-4 border-b border-gray-300">
              <div className="space-y-2">
                <div className="text-1xl text-gray-700 font-medium">2021.08.02</div>
                <div className="flex items-start gap-3">
                  <span className="bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">お知らせ</span>
                  <Link href="/diecast-navi/news/475.html" className="text-gray-800 hover:text-red-500 transition-colors leading-relaxed text-1xl">
                    ダイカスト鋳造 コストダウンNavi ホームページを公開しました
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="text-center">
          <Link
            href="/diecast-navi/news"
            className="inline-block bg-red-500 hover:bg-red-600 text-white px-8 py-3 rounded-full font-medium transition-colors shadow-md"
          >
            一覧へ
          </Link>
        </div>
      </div>
    </div>
  )
}

// Contact Section
function ContactSection() {
  return (
    <div className="bg-white">
      <div className="container-huari py-16">
        {/* Top Three Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
          {/* FAQ Card */}
          <Link href="/diecast-navi/faq" className="block bg-white border-2 border-red-400 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
            <h3 className="text-3xl font-bold text-red-500 mb-4">FAQ</h3>
            <p className="text-sm text-gray-700 leading-relaxed">
              ダイカスト鋳造 コストダウンNaviに<br />
              寄せられるよくある質問
            </p>
          </Link>

          {/* 用語集 Card */}
          <Link href="/diecast-navi/glossary" className="block bg-white border-2 border-red-400 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
            <h3 className="text-3xl font-bold text-red-500 mb-4">用語集</h3>
            <p className="text-sm text-gray-700 leading-relaxed">
              ダイカスト鋳造 コストダウンNaviに<br />
              まつわるキーワード
            </p>
          </Link>

          {/* サービス提供の流れ Card */}
          <Link href="/diecast-navi/flow" className="block bg-white border-2 border-red-400 rounded-lg p-6 text-center hover:shadow-lg transition-shadow">
            <h3 className="text-2xl font-bold text-red-500 mb-4">サービス提供<br />の流れ</h3>
            <p className="text-sm text-gray-700 leading-relaxed">
              お問い合わせから<br />
              製品提供の流れ
            </p>
          </Link>
        </div>
      </div>

      {/* Contact Information with Pink Background */}
      <div
        className="relative py-16 bg-gradient-to-r from-pink-100 via-pink-50 to-pink-100 overflow-hidden"
        style={{
          backgroundImage: `
            linear-gradient(135deg, rgba(255, 182, 193, 0.3) 0%, rgba(255, 240, 245, 0.8) 50%, rgba(255, 182, 193, 0.3) 100%),
            url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffb6c1' fill-opacity='0.1'%3E%3Cpolygon points='30,0 60,30 30,60 0,30'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
          `
        }}
      >
        {/* Geometric shapes */}
        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-32 h-32 bg-pink-200 opacity-30 rotate-45"></div>
        <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-32 h-32 bg-pink-200 opacity-30 rotate-45"></div>

        <div className="relative z-10 container-huari text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">お気軽にご相談ください</h2>
          <p className="text-gray-700 mb-8 leading-relaxed">
            ダイカスト鋳造 コストダウンNaviは、<br />
            特許製法であるダイカストカセットシステムを軸とした他社には真似できない強みにより、<br />
            皆様に高品質・コストダウン・製造リードタイムの短縮といったメリットを提供します。
          </p>

          {/* Large Phone Number */}
          <div className="mb-8">
            <div className="flex items-center justify-center mb-2">
              <span className="text-red-500 text-2xl mr-2">📞</span>
              <span className="text-4xl md:text-5xl font-bold text-gray-800">+(86) 0574-86118978</span>
            </div>
          </div>

          <Link
            href="/diecast-navi/contact"
            className="inline-block bg-red-500 hover:bg-red-600 text-white px-8 py-3 rounded-full font-bold text-lg transition-colors shadow-lg"
          >
            ご相談・お問い合わせはコチラ
          </Link>
        </div>
      </div>
    </div>
  )
}

import Link from "next/link"
import Image from "next/image"

export function IndustryApplications() {
  return (
    <div className="py-12 bg-gray-100">
      <div className="container-huari">
        {/* Company and Recruitment info cards - horizontal layout like original */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Link href="/company" className="group block">
            <div className="relative overflow-hidden rounded-lg">
              <Image
                src="images/industry/f1.jpg"
                alt="企業情報"
                width={553}
                height={323}
                className="w-full h-auto object-cover group-hover:opacity-80 transition-opacity"
                style={{
                  aspectRatio: '553/230',
                  objectPosition: 'center center'
                }}
              />
              {/* Text overlay in the center of the image */}
              <div className="absolute inset-0 flex items-center justify-center">
                <h3 className="text-4xl font-bold text-white drop-shadow-lg">企業情報</h3>
              </div>
            </div>
          </Link>

          <Link href="/equipment" className="group block">
            <div className="relative overflow-hidden rounded-lg">
              <Image
                src="images/manufacturer_function/m5.png"
                alt="設備一覧"
                width={553}
                height={323}
                className="w-full h-auto object-cover group-hover:opacity-80 transition-opacity"
                style={{
                  aspectRatio: '553/230',
                  objectPosition: 'center center'
                }}
              />
              {/* Text overlay in the center of the image */}
              <div className="absolute inset-0 flex items-center justify-center">
                <h3 className="text-4xl font-bold text-white drop-shadow-lg">設備一覧</h3>
              </div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  )
}

import { Metadata } from "next"
import Image from "next/image"
import Link from "next/link"
import { Mail } from "lucide-react"

export const metadata: Metadata = {
  title: "社会貢献活動 | 寧波市北倫華日金属製品有限公司",
  description: "寧波市北倫華日金属製品有限公司の社会貢献活動、環境への取り組み、地域社会への貢献についてご紹介します。",
}

export default function CompanyCSRPage() {
  return (
    <div className="min-h-screen">
      {/* Page Header */}
      <div className="relative w-full h-[300px] overflow-hidden">
        <Image
          src="/images/company/company-intro.png"
          alt="社会貢献活動"
          fill
          priority
          className="object-cover"
        />
        <div className="absolute inset-0 bg-black/50 flex items-center">
          <div className="container-huari">
            <h1 className="text-3xl md:text-4xl font-bold text-white">社会貢献活動</h1>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-white py-3 border-b border-gray-200">
        <div className="container-huari">
          <nav className="flex text-sm">
            <Link href="/" className="text-gray-500 hover:text-primary transition-colors">
              HOME
            </Link>
            <span className="mx-2 text-gray-400">&gt;</span>
            <Link href="/company" className="text-gray-500 hover:text-primary transition-colors">
              企業情報
            </Link>
            <span className="mx-2 text-gray-400">&gt;</span>
            <span className="text-gray-700">社会貢献活動</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-12 bg-white">
        <div className="container-huari">
          <div className="max-w-4xl mx-auto">
            {/* CSR Philosophy Section */}
            <div className="mb-16">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-huari-dark mb-6">CSR基本方針</h2>
                <p className="text-lg text-gray-700 leading-relaxed">
                  寧波市北倫華日金属製品有限公司は、「持続可能な社会の実現」を目指し、<br />
                  環境保護、地域社会への貢献、従業員の福祉向上に積極的に取り組んでいます。
                </p>
              </div>
            </div>

            {/* Environmental Initiatives Section */}
            <div className="mb-16">
              <div className="bg-huari-red text-white p-6 mb-8">
                <h2 className="text-2xl font-bold">環境への取り組み</h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-bold text-huari-dark mb-4">省エネルギー活動</h3>
                  <ul className="text-gray-700 space-y-2">
                    <li>• LED照明への全面切り替え</li>
                    <li>• 高効率設備の導入</li>
                    <li>• エネルギー使用量の継続的監視</li>
                    <li>• 再生可能エネルギーの活用検討</li>
                  </ul>
                </div>
                
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-bold text-huari-dark mb-4">廃棄物削減</h3>
                  <ul className="text-gray-700 space-y-2">
                    <li>• 金属スクラップのリサイクル推進</li>
                    <li>• 包装材料の削減</li>
                    <li>• 工程改善による歩留まり向上</li>
                    <li>• ゼロエミッション活動</li>
                  </ul>
                </div>
                
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-bold text-huari-dark mb-4">水資源保護</h3>
                  <ul className="text-gray-700 space-y-2">
                    <li>• 工業用水の循環利用</li>
                    <li>• 排水処理設備の高度化</li>
                    <li>• 水使用量の削減目標設定</li>
                    <li>• 地下水保護への配慮</li>
                  </ul>
                </div>
                
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-bold text-huari-dark mb-4">大気環境保全</h3>
                  <ul className="text-gray-700 space-y-2">
                    <li>• 排ガス処理装置の設置</li>
                    <li>• VOC排出量の削減</li>
                    <li>• 粉塵対策の徹底</li>
                    <li>• 環境監視の継続実施</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Community Contribution Section */}
            <div className="mb-16">
              <div className="bg-huari-red text-white p-6 mb-8">
                <h2 className="text-2xl font-bold">地域社会への貢献</h2>
              </div>
              
              <div className="space-y-8">
                <div className="border-l-4 border-huari-red pl-6">
                  <h3 className="text-xl font-bold text-huari-dark mb-3">地域雇用の創出</h3>
                  <p className="text-gray-700 leading-relaxed">
                    地域住民の雇用機会を積極的に創出し、技術研修や職業訓練を通じて地域の人材育成に貢献しています。
                    また、地元の教育機関との連携により、実習生の受け入れや技術指導を行っています。
                  </p>
                </div>
                
                <div className="border-l-4 border-huari-red pl-6">
                  <h3 className="text-xl font-bold text-huari-dark mb-3">地域経済の活性化</h3>
                  <p className="text-gray-700 leading-relaxed">
                    地元企業との取引を優先し、地域のサプライチェーンの強化に努めています。
                    また、地域のイベントや活動への協賛を通じて、地域経済の活性化に貢献しています。
                  </p>
                </div>
                
                <div className="border-l-4 border-huari-red pl-6">
                  <h3 className="text-xl font-bold text-huari-dark mb-3">教育支援活動</h3>
                  <p className="text-gray-700 leading-relaxed">
                    地域の学校への設備寄贈や奨学金制度の設立により、教育環境の向上に貢献しています。
                    工場見学の受け入れや出張授業を通じて、ものづくりの魅力を次世代に伝えています。
                  </p>
                </div>
              </div>
            </div>

            {/* Employee Welfare Section */}
            <div className="mb-16">
              <div className="bg-huari-red text-white p-6 mb-8">
                <h2 className="text-2xl font-bold">従業員の福祉向上</h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-bold text-huari-dark mb-4">働きやすい職場環境</h3>
                  <ul className="text-gray-700 space-y-2">
                    <li>• 安全衛生管理の徹底</li>
                    <li>• 労働時間の適正管理</li>
                    <li>• 職場環境の継続的改善</li>
                    <li>• ハラスメント防止対策</li>
                  </ul>
                </div>
                
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-bold text-huari-dark mb-4">人材育成・研修</h3>
                  <ul className="text-gray-700 space-y-2">
                    <li>• 技術研修プログラムの充実</li>
                    <li>• 資格取得支援制度</li>
                    <li>• キャリア開発支援</li>
                    <li>• 海外研修機会の提供</li>
                  </ul>
                </div>
                
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-bold text-huari-dark mb-4">福利厚生の充実</h3>
                  <ul className="text-gray-700 space-y-2">
                    <li>• 健康診断の定期実施</li>
                    <li>• 社員食堂の運営</li>
                    <li>• レクリエーション活動</li>
                    <li>• 住宅手当・交通費支給</li>
                  </ul>
                </div>
                
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-bold text-huari-dark mb-4">ダイバーシティ推進</h3>
                  <ul className="text-gray-700 space-y-2">
                    <li>• 性別・年齢を問わない採用</li>
                    <li>• 外国人労働者の積極採用</li>
                    <li>• 障がい者雇用の促進</li>
                    <li>• 多様な働き方の支援</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Future Goals Section */}
            <div className="mb-16">
              <div className="bg-huari-red text-white p-6 mb-8">
                <h2 className="text-2xl font-bold">今後の目標</h2>
              </div>
              
              <div className="bg-gray-50 p-8 rounded-lg">
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-huari-dark mb-4">2030年に向けた取り組み</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="bg-huari-red text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-2xl font-bold">30%</span>
                    </div>
                    <h4 className="font-bold text-huari-dark mb-2">CO2削減</h4>
                    <p className="text-sm text-gray-600">2020年比30%削減を目標</p>
                  </div>
                  
                  <div className="text-center">
                    <div className="bg-huari-red text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-2xl font-bold">100</span>
                    </div>
                    <h4 className="font-bold text-huari-dark mb-2">地域雇用</h4>
                    <p className="text-sm text-gray-600">新規雇用100名創出</p>
                  </div>
                  
                  <div className="text-center">
                    <div className="bg-huari-red text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-2xl font-bold">0</span>
                    </div>
                    <h4 className="font-bold text-huari-dark mb-2">ゼロエミッション</h4>
                    <p className="text-sm text-gray-600">廃棄物ゼロを目指す</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Section */}
          <div className="bg-gray-50 p-8 rounded-lg text-center">
            <h2 className="text-2xl font-bold text-huari-dark mb-4">CONTACT US</h2>
            <h3 className="text-xl text-huari-dark mb-6">お見積り・ご相談はこちら</h3>

            <div className="flex flex-col md:flex-row items-center justify-center gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-huari-red mb-2">+(86) 0574-86118978</div>
                <p className="text-sm text-gray-600">営業時間 9：00～17：00（土日祝 定休）</p>
              </div>

              <Link
                href="/contact"
                className="inline-flex items-center bg-huari-red text-white px-8 py-4 rounded-sm hover:bg-huari-red/90 transition-colors font-medium"
              >
                <Mail size={24} className="mr-2" />
                お問い合わせ
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

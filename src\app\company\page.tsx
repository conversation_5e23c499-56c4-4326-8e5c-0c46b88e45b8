import { Metadata } from "next"
import Image from "next/image"
import Link from "next/link"
import { Mail } from "lucide-react"

export const metadata: Metadata = {
  title: "企業情報 | 寧波市北倫華日金属製品有限公司",
  description: "寧波市北倫華日金属製品有限公司の企業情報をご紹介します。2002年設立以来、金型設計・製造、精密機械加工、ダイカスト、電気メッキを一体化したサービスを提供しています。",
}

export default function CompanyPage() {
  return (
    <div className="min-h-screen">
      {/* Page Header */}
      <div className="relative w-full h-[300px] overflow-hidden">
        <Image
          src="/images/company/company-intro.png"
          alt="企業情報"
          fill
          priority
          className="object-cover"
        />
        <div className="absolute inset-0 bg-black/50 flex items-center">
          <div className="container-huari">
            <h1 className="text-3xl md:text-4xl font-bold text-white">企業情報</h1>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-white py-3 border-b border-gray-200">
        <div className="container-huari">
          <nav className="flex text-sm">
            <Link href="/" className="text-gray-500 hover:text-primary transition-colors">
              HOME
            </Link>
            <span className="mx-2 text-gray-400">&gt;</span>
            <span className="text-gray-700">企業情報</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-12 bg-white">
        <div className="container-huari">
          {/* Company Introduction Section */}
          <div className="max-w-4xl mx-auto">
            {/* Since 2002 */}
            <div className="text-center mb-12">
              <div className="text-4xl font-bold text-huari-red mb-6">SINCE 2002</div>
              <h2 className="text-2xl font-bold text-huari-dark mb-8">寧波市北倫華日金属製品有限公司</h2>
            </div>

            {/* Company Description */}
            <div className="mb-16">
              <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                <p className="mb-6">
                  寧波市北倫華日金属製品有限公司は、金型設計・製造、精密機械加工、亜鉛・アルミニウム合金ダイカスト、電気メッキを一体化した株式会社です。当社は主に通信、自動車・オートバイ、照明器具、電気機器、錠前、モーターケース、金属部品などの業界向けの各種精密製品および表面処理を製造しています。
                </p>
                
                <p className="mb-6">
                  当社は「中国ダイカスト金型の郷」として名高い寧波北倫に位置し、地理的に優れた立地で、寧波港に隣接し、交通の便が良く、敷地面積8,000平方メートル、建築面積12,000平方メートルを有しています。
                </p>
                
                <p>
                  企業設立以来、常に人本主義、科学技術革新、品質第一、サービス至上の経営方針を堅持し、ISO9001およびIATF16949の基準に厳格に従い、品質管理システムを全面的に実施し、「製品は人格のごとく、ブランド先行、品質で勝つ」という製品理念を提唱し、誠実で堅実な作風、開拓進取の精神および良好なアフターサービスにより、会社の持続的、健全、安定した発展を実現し、同業界の最前線に立っています。
                </p>
              </div>
            </div>

            {/* Company Image */}
            <div className="mb-16">
              <div className="relative w-full h-[400px] rounded-lg overflow-hidden">
                <Image
                  src="/images/company/company-intro.png"
                  alt="寧波市北倫華日金属製品有限公司"
                  fill
                  className="object-cover"
                />
              </div>
            </div>

            {/* Key Features Section */}
            <div className="mb-16">
              <div className="bg-huari-red text-white p-6 mb-8">
                <h2 className="text-2xl font-bold">企業の特徴</h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-bold text-huari-dark mb-4">総合的なサービス</h3>
                  <p className="text-gray-700">
                    金型設計・製造から精密機械加工、ダイカスト、電気メッキまで、一貫したサービスを提供しています。
                  </p>
                </div>
                
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-bold text-huari-dark mb-4">品質管理</h3>
                  <p className="text-gray-700">
                    ISO9001およびIATF16949の基準に従い、厳格な品質管理システムを実施しています。
                  </p>
                </div>
                
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-bold text-huari-dark mb-4">優れた立地</h3>
                  <p className="text-gray-700">
                    寧波港に隣接する優れた立地で、交通の便が良く、効率的な物流を実現しています。
                  </p>
                </div>
                
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-xl font-bold text-huari-dark mb-4">豊富な実績</h3>
                  <p className="text-gray-700">
                    2002年の設立以来、多様な業界向けの精密製品を製造し、豊富な実績を積み重ねています。
                  </p>
                </div>
              </div>
            </div>

            {/* New Factory Section */}
            <div className="mb-16">
              <div className="bg-huari-red text-white p-6 mb-8">
                <h2 className="text-2xl font-bold">新工場</h2>
              </div>

              <div className="mb-8">
                <p className="text-gray-700 leading-relaxed text-lg">
                  新工場は現在建設準備中で、敷地面積41,000平方メートル、建築面積58,000平方メートルを予定しています。デジタル化・知能化された生産車間を構築し、30台のダイカスト設備、200台の高精度加工設備、全自動表面塗装生産ライン、国際レベルの実験・検査センターなどを配置し、2026年末の完成を目指しています。
                </p>
              </div>

              {/* New Factory Specifications */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div className="text-center bg-gray-50 p-6 rounded-lg">
                  <div className="text-3xl font-bold text-huari-red mb-2">41,000</div>
                  <p className="text-sm text-gray-600">敷地面積（㎡）</p>
                </div>

                <div className="text-center bg-gray-50 p-6 rounded-lg">
                  <div className="text-3xl font-bold text-huari-red mb-2">58,000</div>
                  <p className="text-sm text-gray-600">建築面積（㎡）</p>
                </div>

                <div className="text-center bg-gray-50 p-6 rounded-lg">
                  <div className="text-3xl font-bold text-huari-red mb-2">30台</div>
                  <p className="text-sm text-gray-600">ダイカスト設備</p>
                </div>

                <div className="text-center bg-gray-50 p-6 rounded-lg">
                  <div className="text-3xl font-bold text-huari-red mb-2">200台</div>
                  <p className="text-sm text-gray-600">高精度加工設備</p>
                </div>
              </div>

              {/* New Factory Images Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="relative h-[250px] rounded-lg overflow-hidden">
                  <Image
                    src="/images/manufacturer_function/m2.jpg"
                    alt="新工場建設予定地"
                    fill
                    className="object-cover hover:scale-105 transition-transform duration-300"
                  />
                </div>

                <div className="relative h-[250px] rounded-lg overflow-hidden">
                  <Image
                    src="/images/manufacturer_function/m3.jpg"
                    alt="新工場設計図"
                    fill
                    className="object-cover hover:scale-105 transition-transform duration-300"
                  />
                </div>

                <div className="relative h-[250px] rounded-lg overflow-hidden">
                  <Image
                    src="/images/manufacturer_function/m4.jpg"
                    alt="新工場設備計画"
                    fill
                    className="object-cover hover:scale-105 transition-transform duration-300"
                  />
                </div>

                <div className="relative h-[250px] rounded-lg overflow-hidden">
                  <Image
                    src="/images/manufacturer_function/m5.png"
                    alt="新工場完成予想図"
                    fill
                    className="object-cover hover:scale-105 transition-transform duration-300"
                  />
                </div>
              </div>

              {/* Completion Timeline */}
              <div className="mt-8 bg-huari-red/10 p-6 rounded-lg text-center">
                <h3 className="text-xl font-bold text-huari-dark mb-4">完成予定</h3>
                <div className="text-4xl font-bold text-huari-red mb-2">2026年末</div>
                <p className="text-gray-700">最新の技術を導入した次世代工場として稼働開始予定</p>
              </div>
            </div>
          </div>

          {/* Contact Section */}
          <div className="bg-gray-50 p-8 rounded-lg text-center">
            <h2 className="text-2xl font-bold text-huari-dark mb-4">CONTACT US</h2>
            <h3 className="text-xl text-huari-dark mb-6">お見積り・ご相談はこちら</h3>

            <div className="flex flex-col md:flex-row items-center justify-center gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-huari-red mb-2">+(86) 0574-86118978</div>
                <p className="text-sm text-gray-600">営業時間 9：00～17：00（土日祝 定休）</p>
              </div>

              <Link
                href="/contact"
                className="inline-flex items-center bg-huari-red text-white px-8 py-4 rounded-sm hover:bg-huari-red/90 transition-colors font-medium"
              >
                <Mail size={24} className="mr-2" />
                お問い合わせ
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { Button } from "@/components/ui/button"
import { useRecaptcha } from '@/hooks/use-recaptcha'

interface FormData {
  name: string
  email: string
  company: string
  phone: string
  message: string
}

interface ContactFormProps {
  onSubmit?: (data: FormData & { recaptchaToken: string }) => Promise<void>
}

export function ContactForm({ onSubmit }: ContactFormProps) {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    company: '',
    phone: '',
    message: '',
  })
  
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [submitMessage, setSubmitMessage] = useState('')
  
  const { isVerified, isLoading, error, executeRecaptcha, resetRecaptcha } = useRecaptcha()

  // Auto-verify reCAPTCHA when form is loaded
  useEffect(() => {
    const timer = setTimeout(() => {
      executeRecaptcha('contact_form_load')
    }, 1000) // Small delay to ensure reCAPTCHA is ready

    return () => clearTimeout(timer)
  }, [executeRecaptcha])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))
  }

  const isFormValid = () => {
    return formData.name.trim() !== '' && 
           formData.email.trim() !== '' && 
           formData.message.trim() !== '' &&
           isVerified
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!isFormValid()) {
      return
    }

    setIsSubmitting(true)
    setSubmitStatus('idle')
    setSubmitMessage('')

    try {
      // Execute reCAPTCHA for form submission
      const token = await executeRecaptcha('contact_form_submit')
      
      if (!token) {
        throw new Error('reCAPTCHA verification failed')
      }

      if (onSubmit) {
        await onSubmit({ ...formData, recaptchaToken: token })
      } else {
        // Default form submission logic - call API
        const response = await fetch('/api/contact', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ...formData, recaptchaToken: token }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Form submission failed')
        }
      }

      setSubmitStatus('success')
      setSubmitMessage('お問い合わせを送信いたしました。ありがとうございます。')
      
      // Reset form
      setFormData({
        name: '',
        email: '',
        company: '',
        phone: '',
        message: '',
      })
      
      // Reset reCAPTCHA
      resetRecaptcha()
      
      // Re-verify reCAPTCHA after reset
      setTimeout(() => {
        executeRecaptcha('contact_form_load')
      }, 1000)

    } catch (error) {
      console.error('Form submission error:', error)
      setSubmitStatus('error')
      setSubmitMessage('送信中にエラーが発生しました。もう一度お試しください。')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="bg-[#f7f7f7] p-6 md:p-8 rounded-md shadow-sm">
      <h2 className="text-xl font-bold mb-6 text-center">お問い合わせフォーム</h2>

      {/* reCAPTCHA Status Indicator */}
      <div className="mb-4 p-3 rounded-md bg-gray-100 border">
        <div className="flex items-center gap-2 text-sm">
          {isLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <span className="text-blue-600">セキュリティ認証を確認中...</span>
            </>
          ) : isVerified ? (
            <>
              <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-green-600">セキュリティ認証が完了しました</span>
            </>
          ) : error ? (
            <>
              <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-red-600">セキュリティ認証でエラーが発生しました</span>
              <button 
                onClick={() => executeRecaptcha('contact_form_retry')}
                className="ml-2 text-blue-600 underline hover:no-underline"
              >
                再試行
              </button>
            </>
          ) : (
            <>
              <div className="w-4 h-4 bg-gray-400 rounded-full"></div>
              <span className="text-gray-600">セキュリティ認証を準備中...</span>
            </>
          )}
        </div>
      </div>

      {/* Submit Status Messages */}
      {submitStatus === 'success' && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
          <p className="text-green-800 text-center">{submitMessage}</p>
        </div>
      )}
      
      {submitStatus === 'error' && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-800 text-center">{submitMessage}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-start">
          <div className="md:text-right pt-2">
            <label className="font-medium">
              <span className="text-huari-red mr-1">*</span>
              お名前
            </label>
          </div>
          <div className="md:col-span-3">
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded-sm focus:outline-none focus:ring-1 focus:ring-primary"
              required
            />
            <p className="text-xs text-gray-500 mt-1">例：山田 太郎</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-start">
          <div className="md:text-right pt-2">
            <label className="font-medium">
              <span className="text-huari-red mr-1">*</span>
              メールアドレス
            </label>
          </div>
          <div className="md:col-span-3">
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded-sm focus:outline-none focus:ring-1 focus:ring-primary"
              required
            />
            <p className="text-xs text-gray-500 mt-1">例：<EMAIL></p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-start">
          <div className="md:text-right pt-2">
            <label className="font-medium">会社名</label>
          </div>
          <div className="md:col-span-3">
            <input
              type="text"
              name="company"
              value={formData.company}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded-sm focus:outline-none focus:ring-1 focus:ring-primary"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-start">
          <div className="md:text-right pt-2">
            <label className="font-medium">電話番号</label>
          </div>
          <div className="md:col-span-3">
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded-sm focus:outline-none focus:ring-1 focus:ring-primary"
            />
            <p className="text-xs text-gray-500 mt-1">例：************</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-start">
          <div className="md:text-right pt-2">
            <label className="font-medium">
              <span className="text-huari-red mr-1">*</span>
              お問い合わせ内容
            </label>
          </div>
          <div className="md:col-span-3">
            <textarea
              name="message"
              value={formData.message}
              onChange={handleInputChange}
              rows={5}
              className="w-full p-2 border border-gray-300 rounded-sm focus:outline-none focus:ring-1 focus:ring-primary"
              required
            />
          </div>
        </div>

        <div className="text-center mt-8">
          <Button 
            type="submit" 
            disabled={!isFormValid() || isSubmitting}
            className="bg-huari-red hover:bg-huari-red/90 text-white px-8 py-3 rounded-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                送信中...
              </div>
            ) : (
              '送信する'
            )}
          </Button>
          
          {!isVerified && !isLoading && (
            <p className="text-xs text-gray-500 mt-2">
              セキュリティ認証が完了するまでお待ちください
            </p>
          )}
        </div>
      </form>
    </div>
  )
}
